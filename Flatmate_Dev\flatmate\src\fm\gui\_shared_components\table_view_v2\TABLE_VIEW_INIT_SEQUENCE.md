# Table View Initialization Sequence Documentation

## Overview
This documents the EXACT sequence of how the table view is initialized in the categorize module, from data loading to final display. This is to stop the endless tail-chasing when trying to make simple changes.

## The Problem
The table view system has become overly complex with multiple layers overriding each other:
1. Auto-sizing gets overridden by configured widths
2. Column visibility gets set multiple times
3. Multiple column management systems conflict
4. Changes don't take effect because of the override chain

## Initialization Flow

### 1. Module Level - CategorizePresenter
**File:** `fm/modules/categorize/cat_presenter.py`

```python
# Data is loaded from database
df = self.database_service.get_transactions_for_categorization()

# Data is passed to view
self.view.set_dataframe(df)
```

### 2. View Level - CatView
**File:** `fm/modules/categorize/_view/cat_view.py`

```python
def set_dataframe(self, df: pd.DataFrame):
    """Set the transactions dataframe to display."""
    self.logger.debug(f"CatView setting dataframe: {len(df)} rows")
    if df is not None and not df.empty:
        self.logger.debug(f"DataFrame columns: {df.columns.tolist()}")
        # Passes to center panel manager
        self.center_panel_manager.set_transactions(df)
```

### 3. Panel Level - TransactionViewPanel
**File:** `fm/modules/categorize/_view/components/center_panel/transaction_view_panel.py`

```python
def set_transactions(self, df: pd.DataFrame):
    """This is where the complexity starts - MULTIPLE OVERRIDES HAPPEN HERE"""
    
    # Step 1: Column management preparation
    column_manager = get_simple_column_manager()
    df_ordered, column_mapping, available_columns = column_manager.prepare_dataframe_with_all_columns(df_copy, "categorize")
    
    # Step 2: Set dataframe (triggers auto-sizing)
    self.transaction_table.set_dataframe(df_ordered)
    
    # Step 3: Set display columns
    self.transaction_table.set_display_columns(available_columns, column_mapping)
    
    # Step 4: Set editable columns
    self.transaction_table.set_editable_columns(['tags'])
    
    # Step 5: Apply column widths (OVERRIDES auto-sizing!)
    self._apply_column_widths(column_mapping)
    
    # Step 6: Apply column visibility (OVERRIDES previous visibility!)
    self._apply_default_column_visibility(available_columns)
```

### 4. Table Widget Level - CustomTableView (fm_table_view.py)
**File:** `fm/gui/_shared_components/table_view/fm_table_view.py`

```python
def set_dataframe(self, df: pd.DataFrame):
    """Delegates to core table view"""
    self.table_view.set_dataframe(df)  # Calls TableViewCore
    
    # Updates toolbar with columns
    columns = list(df.columns)
    self.toolbar.set_columns(columns)

def set_display_columns(self, columns, column_names=None):
    """Sets display columns and updates toolbar"""
    self._display_columns = columns
    self.toolbar.set_columns(columns, column_names)
    self.table_view.set_display_columns(columns, column_names)

def set_column_widths(self, width_map: Dict[str, int]):
    """Delegates to core table view"""
    self.table_view.set_column_widths(width_map)
```

### 5. Core Table Level - TableViewCore
**File:** `fm/gui/_shared_components/table_view/components/table_view_core.py`

```python
def set_dataframe(self, df: pd.DataFrame):
    """FIRST auto-sizing happens here"""
    self._model.set_dataframe(df)
    
    # Auto-resize columns to contents with maximum width limit
    self._auto_resize_columns_with_limit()  # NEW: 40 char limit
    
    # Restore saved column widths (OVERRIDES auto-sizing!)
    header = self.horizontalHeader()
    for col, width in self._column_widths.items():
        if col < self._model.columnCount():
            header.resizeSection(col, width)

def set_column_widths(self, width_map: Dict[str, int], max_chars=40):
    """FINAL width setting - OVERRIDES everything above"""
    # Auto-resize all columns to content AGAIN
    self.resizeColumnsToContents()
    
    # Apply configured widths with max limit
    for col_idx in range(self._model.columnCount()):
        col_name = self._model.headerData(col_idx, Qt.Horizontal)
        if col_name in width_map:
            configured_width = width_map[col_name] * char_width
            final_width = min(configured_width, max_pixel_width)
            header.resizeSection(col_idx, final_width)
```

## The Override Chain (Why Changes Don't Work)

1. **Auto-sizing** happens in `set_dataframe()` 
2. **Saved widths** override auto-sizing
3. **Configured widths** override saved widths  
4. **Column visibility** gets set multiple times

## Where Column Widths Come From

### StandardColumns Configuration
**File:** `fm/core/standards/fm_standard_columns.py`

```python
@classmethod
def get_standard_column_widths(cls):
    """These are the FINAL widths that override everything"""
    return {
        cls.DATE.value: 12,        # 'Date': 12 characters
        cls.DETAILS.value: 40,     # 'Details': 40 characters  
        cls.AMOUNT.value: 12,      # 'Amount': 12 characters
        cls.ACCOUNT.value: 15,     # 'Account': 15 characters
        cls.BALANCE.value: 12,     # 'Balance': 12 characters
        cls.PAYMENT_TYPE.value: 15,# 'Payment Type': 15 characters
        cls.SOURCE_FILENAME.value: 20, # 'Source Filename': 20 characters
        'Tags': 20,                # Non-standard columns
        'Category': 20,
        'Notes': 30
    }
```

### Width Application in TransactionViewPanel
```python
def _apply_column_widths(self, column_mapping):
    """This OVERRIDES all auto-sizing"""
    standard_widths = StandardColumns.get_standard_column_widths()
    width_map = {}
    
    for db_name, display_name in column_mapping.items():
        if display_name in standard_widths:
            width_map[display_name] = standard_widths[display_name]
        else:
            width_map[display_name] = 20  # Default fallback
    
    # This call OVERRIDES all previous sizing
    self.transaction_table.set_column_widths(width_map)
```

## How to Make Changes

### To Change Column Widths:
**Edit:** `fm/core/standards/fm_standard_columns.py`
```python
# Change the values in get_standard_column_widths()
cls.DETAILS.value: 50,  # Make Details column wider
```

### To Enable Auto-Sizing:
**Option 1:** Comment out the width application in `TransactionViewPanel`:
```python
# self._apply_column_widths(column_mapping)  # Comment this out
```

**Option 2:** Modify `get_standard_column_widths()` to return `None` for auto-sized columns:
```python
# Return None for columns that should auto-size
cls.DETAILS.value: None,  # Will auto-size with 40 char limit
```

### To Change the 40-Character Limit:
**Edit:** `table_view_core.py`
```python
def _auto_resize_columns_with_limit(self, max_chars=60):  # Change from 40 to 60
```

## Current Issues

1. **Too many override layers** - auto-sizing gets overridden multiple times
2. **Configuration scattered** - widths in StandardColumns, limits in TableViewCore
3. **No single source of truth** - multiple systems fighting each other
4. **Saved widths interfere** - user resizing gets restored and overrides everything

## Recommended Fix

**Simplify to single configuration point:**
1. Move all width logic to StandardColumns
2. Remove auto-sizing overrides
3. Make width configuration respect max limits
4. Clear saved widths when changing configuration

## Quick Fixes for Common Changes

### Make All Columns Auto-Size (40 char limit):
**File:** `transaction_view_panel.py` - Comment out line 197:
```python
# self.transaction_table.set_column_widths(width_map)  # DISABLE THIS LINE
```

### Change Details Column Width:
**File:** `fm_standard_columns.py` - Line 84:
```python
cls.DETAILS.value: 60,  # Change from 40 to 60 characters
```

### Change Auto-Size Limit:
**File:** `table_view_core.py` - Line 87:
```python
def _auto_resize_columns_with_limit(self, max_chars=60):  # Change from 40
```

### Clear Saved Column Widths:
**File:** `table_view_core.py` - Add to `set_dataframe()`:
```python
def set_dataframe(self, df: pd.DataFrame):
    self._model.set_dataframe(df)
    self._auto_resize_columns_with_limit()

    # COMMENT OUT saved width restoration:
    # header = self.horizontalHeader()
    # for col, width in self._column_widths.items():
    #     if col < self._model.columnCount():
    #         header.resizeSection(col, width)
```

## The Root Problem

**The system has 4 different width-setting mechanisms that override each other:**
1. Auto-sizing to content
2. Saved user widths
3. Configured standard widths
4. Maximum width limits

**They execute in this order, so the last one wins. That's why your auto-sizing changes had no effect - they were immediately overridden by the configured widths.**
