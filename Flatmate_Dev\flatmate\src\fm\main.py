#!/usr/bin/env python3
"""
Main entry point for the FlatMate application.
"""

import sys
import os
from pathlib import Path

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont
from .core.config import config
from .core.config.paths import AppPaths
from .core.config.keys import ConfigKeys
from .core.services.event_bus import global_event_bus
from .core.services.logger import setup_logging, log
from .gui.main_window import MainWindow
from .module_coordinator import ModuleCoordinator


def initialize_application() -> tuple:
    """
    Comprehensive application initialization.

    Sets up logging, configuration, and ensures required directories exist.

    Returns:
        tuple: (QApplication, MainWindow, ModuleCoordinator) - The main application objects
    """
    # 0. Set DPI awareness BEFORE any Qt objects are created
    os.environ["QT_ENABLE_HIGHDPI_SCALING"] = "1"
    os.environ["QT_AUTO_SCREEN_SCALE_FACTOR"] = "1"
    os.environ["QT_SCALE_FACTOR_ROUNDING_POLICY"] = "RoundPreferFloor"

    # Set Qt application attributes before creating QApplication
    QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.RoundPreferFloor)

    # 1. Ensure all required directories exist
    AppPaths.ensure_directories()

    # 1. Initialize logging. Logger will configure itself from global config.
    logger = setup_logging()
    
    try:
        # 2. Publish debug mode event if enabled
        if config.get_value(ConfigKeys.App.DEBUG_MODE, False):
            global_event_bus.publish('debug_mode_enabled')
        
        # Directory creation is handled by SystemPaths/UserPaths in config.py and by logger.py for its specific log directory.
        # The loop for core_paths has been removed.

        # 3. Initialize and show the main application window
        app = QApplication(sys.argv)

        # Set high DPI scaling attributes
        app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

        # Set default font with proper rendering
        font = QFont("Segoe UI", 9)
        font.setHintingPreference(QFont.PreferFullHinting)
        app.setFont(font)
        
        # 5. Load and apply styles
        from .gui.styles import apply_styles
        apply_styles(app)
        
        # 4. Create Main Window
        main_window = MainWindow()
        #main_window.resize(1200, 800)  # Set initial size 
        main_window.show()
        
        # 5. Create and initialize coordinator
        logger.info("\n=== Setting up Module Coordinator ===")
        coordinator = ModuleCoordinator(main_window)
        coordinator.initialize_modules()
        
        # Set the coordinator in the main window
        main_window.set_module_manager(coordinator)
        
        # 6. Start coordinator
        coordinator.start()  # Start coordinator to transition to home
        
        logger.info("\n=== Application Ready ===")
        return app, main_window, coordinator
        
    except Exception as e:
        logger.critical(f"Fatal error during application initialization: {e}")
        import traceback
        logger.critical(f"Exception details: {traceback.format_exc()}")
        raise


def main():
    """Application entry point."""
    app, window, coordinator = initialize_application()
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
