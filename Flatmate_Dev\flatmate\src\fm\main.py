#!/usr/bin/env python3
"""
Main entry point for the FlatMate application.
"""

import sys
from pathlib import Path

from PySide6.QtWidgets import QApplication
from .core.config import config
from .core.config.paths import AppPaths
from .core.config.keys import ConfigKeys
from .core.services.event_bus import global_event_bus
from .core.services.logger import setup_logging, log
from .gui.main_window import MainWindow
from .module_coordinator import ModuleCoordinator


def initialize_application() -> tuple:
    """
    Comprehensive application initialization.
    
    Sets up logging, configuration, and ensures required directories exist.
    
    Returns:
        tuple: (QApplication, MainWindow, ModuleCoordinator) - The main application objects
    """
    # 0. Ensure all required directories exist

    AppPaths.ensure_directories()

    # 1. Initialize logging. Logger will configure itself from global config.
    logger = setup_logging()
    
    try:
        # 2. Publish debug mode event if enabled
        if config.get_value(ConfigKeys.App.DEBUG_MODE, False):
            global_event_bus.publish('debug_mode_enabled')
        
        # Directory creation is handled by SystemPaths/UserPaths in config.py and by logger.py for its specific log directory.
        # The loop for core_paths has been removed.

        # 3. Initialize and show the main application window
        app = QApplication(sys.argv)
        
        # 5. Load and apply styles
        from .gui.styles import apply_styles
        apply_styles(app)
        
        # 4. Create Main Window
        main_window = MainWindow()
        #main_window.resize(1200, 800)  # Set initial size 
        main_window.show()
        
        # 5. Create and initialize coordinator
        logger.info("\n=== Setting up Module Coordinator ===")
        coordinator = ModuleCoordinator(main_window)
        coordinator.initialize_modules()
        
        # Set the coordinator in the main window
        main_window.set_module_manager(coordinator)
        
        # 6. Start coordinator
        coordinator.start()  # Start coordinator to transition to home
        
        logger.info("\n=== Application Ready ===")
        return app, main_window, coordinator
        
    except Exception as e:
        logger.critical(f"Fatal error during application initialization: {e}")
        import traceback
        logger.critical(f"Exception details: {traceback.format_exc()}")
        raise


def main():
    """Application entry point."""
    app, window, coordinator = initialize_application()
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
