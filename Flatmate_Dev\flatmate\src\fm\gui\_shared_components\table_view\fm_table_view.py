"""
Enhanced Table View - Complete Table Solution

This is the main entry point for the enhanced table system. It provides a complete
table widget with filtering, column management, and export capabilities.

This combines the core table view with a modular toolbar to provide the full user experience.
Uses the properly modularized components instead of recreating functionality inline.
"""

from typing import Dict, List, Union
import pandas as pd

from PySide6.QtCore import Qt
from PySide6.QtGui import QAction
from PySide6.QtWidgets import QWidget, QVBoxLayout, QFrame, QMenu

from .components.table_view_core import TableViewCore
from .components.toolbar import TableViewToolbar


class CustomTableView(QWidget):
    """
    Complete table solution with filter controls and export options.
    
    This is the main component users should import and use. It combines:
    - Core table view (TableViewCore) 
    - Filter toolbar with controls
    - Export functionality
    - Column management
    
    Usage:
        table = EnhancedTableView()
        table.set_dataframe(df)
        table.set_display_columns(['col1', 'col2'], {'col1': 'Column 1'})
    """
    
    def __init__(self, parent=None):
        """Initialize the complete enhanced table solution using modular components."""
        super().__init__(parent)

        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)  # Minimal spacing between toolbar and table

        # Modular toolbar component
        self.toolbar = TableViewToolbar()
        layout.addWidget(self.toolbar)

        # Separator
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(separator)

        # Core table view (the actual table)
        self.table_view = TableViewCore()
        layout.addWidget(self.table_view, 1)  # Give table stretch priority

        # Connect toolbar signals to table functionality
        self._connect_signals()

    def _connect_signals(self):
        """Connect toolbar signals to table functionality."""
        # Connect filter signals
        self.toolbar.filter_applied.connect(self._on_filter_applied)
        self.toolbar.filters_cleared.connect(self._on_filters_cleared)

        # Connect action signals
        self.toolbar.column_visibility_requested.connect(self._show_column_visibility)
        self.toolbar.csv_export_requested.connect(self._export_csv)
        self.toolbar.excel_export_requested.connect(self._export_excel)

    # Expose table_view signals for external use
    @property
    def row_selected(self):
        """Signal emitted when a row is selected."""
        return self.table_view.row_selected

    @property
    def cell_edited(self):
        """Signal emitted when a cell is edited."""
        return self.table_view.cell_edited

    def _on_filter_applied(self, column, pattern):
        """Handle filter applied signal from toolbar."""
        self.table_view.set_column_filter(column, pattern)

    def _on_filters_cleared(self):
        """Handle filters cleared signal from toolbar."""
        self.table_view.clear_filters()

    def _show_column_visibility(self):
        """Show column visibility menu."""
        menu = QMenu(self)

        for col in range(self.table_view._model.columnCount()):
            col_name = self.table_view._model.headerData(col, Qt.Horizontal)
            action = QAction(str(col_name), self)
            action.setCheckable(True)
            action.setChecked(not self.table_view.isColumnHidden(col))
            action.triggered.connect(lambda checked, column=col:
                                    self.table_view.setColumnHidden(column, not checked))
            menu.addAction(action)

        # Show menu relative to the column group button (not far left of toolbar)
        column_group = self.toolbar.column_group
        if column_group and hasattr(column_group, 'column_visibility_button'):
            # Position menu below the column visibility button
            button_pos = column_group.column_visibility_button.mapToGlobal(
                column_group.column_visibility_button.rect().bottomLeft())
            menu.exec(button_pos)
        else:
            # Fallback to center of toolbar if button not found
            toolbar_center = self.toolbar.rect().center()
            menu.exec(self.toolbar.mapToGlobal(toolbar_center))

    def _export_csv(self):
        """Handle CSV export request."""
        self.table_view._export_data("csv")

    def _export_excel(self):
        """Handle Excel export request."""
        self.table_view._export_data("excel")

    # Public API - delegate to table_view and update toolbar
    def set_dataframe(self, df: pd.DataFrame):
        """Set the table data from a pandas DataFrame."""
        self.table_view.set_dataframe(df)

        # Update toolbar with available columns
        columns = list(df.columns)
        self.toolbar.set_columns(columns)

    def get_dataframe(self) -> pd.DataFrame:
        """Get the current data as a pandas DataFrame."""

        return self.table_view.get_dataframe()

    def set_editable_columns(self, columns: List[Union[int, str]]):
        """Set which columns should be editable."""
        self.table_view.set_editable_columns(columns)

    def set_display_columns(self, columns, column_names=None):
        """Set which columns to display and their display names.

        Args:
            columns: List of database column names to display
            column_names: Dictionary mapping database column names to display names
        """
        print(f"DEBUG set_display_columns: columns={columns}")
        print(f"DEBUG set_display_columns: column_names={column_names}")

        # Store the display columns for reference
        self._display_columns = columns

        # Update toolbar with display columns and names
        self.toolbar.set_columns(columns, column_names)

        # Set display columns in the table view
        self.table_view.set_display_columns(columns, column_names)

    def set_column_widths(self, width_map: Dict[str, int]):
        """Set custom column widths.
        
        Args:
            width_map: Dictionary mapping column names to widths (in characters)
        """
        self.table_view.set_column_widths(width_map)



