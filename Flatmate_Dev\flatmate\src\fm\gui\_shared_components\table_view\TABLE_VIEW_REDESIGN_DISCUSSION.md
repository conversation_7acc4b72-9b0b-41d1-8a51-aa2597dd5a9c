# Table View System Redesign Discussion

## Current Problem
The table view system is a mess of competing configuration layers that override each other. Simple changes require hunting through multiple files and understanding a complex initialization chain. This is unacceptable for a core UI component.

## App-Wide Pattern
This redesign follows the **App-Wide Custom Widget Pattern** documented in [APP_WIDE_WIDGET_PATTERN.md](../APP_WIDE_WIDGET_PATTERN.md). The table view serves as the primary example and testing ground for this pattern, which should be applied to all custom widgets in the application.

**Key Pattern Elements:**
- `configure()` method sets instance runtime defaults
- `set_content()` methods provide data
- `show()` method triggers display and applies configuration
- Dynamic methods allow runtime changes
- Consistent naming conventions across all widgets

## Design Goals

### What Modules Want
- **Capable**: Full-featured table with filtering, sorting, export, column management
- **Attractive**: Good visual design that fits the app theme
- **User-friendly**: Intuitive controls, responsive behavior
- **Configurable**: Easy to customize for module-specific needs
- **Discoverable**: Configuration options are obvious and well-documented
- **Simple**: Minimal code to get a working table
- **Flexible**: Toolbar optional, order of operations shouldn't matter
- **Data-aware**: Module controls editability based on data knowledge

### Key Responsibilities

#### Module View Component Responsibility
- **Knows the data**: Has the DataFrame with actual column names
- **Knows editability**: Understands which columns can/should be editable
- **Database protection**: Ensures non-editable columns are protected at DB level
- **Business logic**: Determines what user should see and interact with

#### Table View Responsibility
- **Display**: Renders data attractively and efficiently
- **Interaction**: Handles user input, filtering, sorting
- **Configuration**: Applies module-specified settings
- **No opinions**: Doesn't decide what's editable - module tells it

### Ideal Module Usage
```python
# Simple case - just works out of the box
self.table_view = CustomTableView()
self.table_view.set_dataframe(df)
self.table_view.show()

# Configured case - clear, discoverable options
self.table_view = CustomTableView()
self.table_view.configure(
    auto_size_columns=True,
    max_column_width=40,
    editable_columns=['tags', 'notes'],  # Module knows which columns are editable
    default_visible_columns=['date', 'details', 'amount'],
    column_widths={'details': 50, 'date': 12},
    show_toolbar=True  # Optional - sometimes just want data display
)
self.table_view.set_dataframe(df)
self.table_view.show()

# Data-only display (no toolbar)
self.table_view = CustomTableView()
self.table_view.configure(show_toolbar=False)
self.table_view.set_dataframe(df)
self.table_view.show()

# Order shouldn't matter (as much as possible)
self.table_view = CustomTableView()
self.table_view.set_dataframe(df)  # Can set data first
self.table_view.configure(editable_columns=['tags'])  # Then configure
self.table_view.show()

# Runtime/Dynamic changes (after table is shown)
self.table_view.set_visible_columns(['date', 'details', 'amount'])
self.table_view.hide_columns(['balance', 'account'])
self.table_view.show_columns(['tags', 'notes'])
self.table_view.hide()  # Hide entire table
self.table_view.show()  # Show it again
self.table_view.resize_column('details', 60)
self.table_view.auto_resize_columns()
```

## Design Options

### Option 1: Single Configuration Method (Recommended)
**Approach**: One `configure()` method with all options as kwargs

**Pros**:
- All configuration in one place
- Easy to discover options (IDE autocomplete)
- Clear precedence - last configure() call wins
- Simple to document and test

**Cons**:
- Large method signature
- Need good parameter validation

**Implementation**:
```python
class CustomTableView:
    def configure(self, 
                 auto_size_columns=True,
                 max_column_width=40,
                 column_widths=None,
                 editable_columns=None,
                 default_visible_columns=None,
                 save_column_state=True,
                 toolbar_groups=None):
        """Configure table behavior and appearance."""
        self._config = TableConfig(
            auto_size_columns=auto_size_columns,
            max_column_width=max_column_width,
            column_widths=column_widths or {},
            editable_columns=editable_columns or [],
            default_visible_columns=default_visible_columns,
            save_column_state=save_column_state,
            toolbar_groups=toolbar_groups or ['filter', 'column', 'export']
        )
        self._apply_configuration()
```

### Option 2: Fluent Interface (Builder Pattern)
**Approach**: Chainable methods for configuration

**Pros**:
- Very readable and discoverable
- Can validate each step
- Flexible order of configuration

**Cons**:
- More complex implementation
- Potential for incomplete configuration

**Implementation**:
```python
self.table_view = (CustomTableView()
    .auto_size_columns(max_width=40)
    .set_editable_columns(['tags', 'notes'])
    .show_toolbar_groups(['filter', 'export'])
    .set_column_widths({'details': 50})
    .build())
```

### Option 3: Configuration Object
**Approach**: Pass a configuration object to constructor

**Pros**:
- Configuration can be reused across modules
- Easy to serialize/deserialize
- Clear separation of config from behavior

**Cons**:
- Extra object to manage
- Less discoverable than direct methods

**Implementation**:
```python
config = TableViewConfig(
    auto_size_columns=True,
    max_column_width=40,
    editable_columns=['tags']
)
self.table_view = CustomTableView(config)
```

### Option 4: Property-Based Configuration
**Approach**: Direct property access for configuration

**Pros**:
- Very simple and direct
- Easy to change individual settings
- Familiar pattern

**Cons**:
- No validation until used
- Order dependencies possible
- Harder to ensure complete configuration

**Implementation**:
```python
self.table_view = CustomTableView()
self.table_view.auto_size_columns = True
self.table_view.max_column_width = 40
self.table_view.editable_columns = ['tags']
```

## Recommended Approach: Option 1 + Sensible Defaults

### Core Principles
1. **Sensible defaults**: Works great out of the box with zero configuration
2. **Single configuration point**: One `configure()` method with all options
3. **Clear precedence**: Configuration overrides defaults, explicit widths override auto-sizing
4. **No hidden overrides**: What you configure is what you get
5. **Discoverable**: Good docstrings, type hints, examples

### Implementation Plan

#### 1. TableViewConfig Class
```python
@dataclass
class TableViewConfig:
    """Configuration for table view behavior and appearance."""
    # Column sizing
    auto_size_columns: bool = True
    max_column_width: int = 40
    min_column_width: int = 8
    column_widths: Dict[str, int] = field(default_factory=dict)

    # Column behavior
    editable_columns: List[str] = field(default_factory=list)
    default_visible_columns: Optional[List[str]] = None
    save_column_state: bool = True

    # Toolbar (optional)
    show_toolbar: bool = True
    toolbar_groups: List[str] = field(default_factory=lambda: ['filter', 'column', 'export'])

    # Appearance
    alternating_rows: bool = True
    show_grid: bool = True
    selection_mode: str = 'rows'  # 'rows', 'cells', 'extended'

    # Column name mapping
    column_display_mapping: Optional[Dict[str, str]] = None  # db_name -> display_name
```

#### 2. Simplified CustomTableView
```python
class CustomTableView(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self._config = TableViewConfig()  # Sensible defaults
        self._dataframe = None
        self._is_shown = False
        self._setup_ui()

    def configure(self, **kwargs):
        """Configure table behavior. See TableViewConfig for all options."""
        # Update config with provided kwargs
        for key, value in kwargs.items():
            if hasattr(self._config, key):
                setattr(self._config, key, value)
            else:
                raise ValueError(f"Unknown configuration option: {key}")

        # Apply configuration if we already have data
        if self._dataframe is not None:
            self._apply_configuration()
        return self  # Allow chaining

    def set_dataframe(self, df, column_mapping=None):
        """Set data. Configuration will be applied when show() is called."""
        self._dataframe = df
        if column_mapping:
            self._config.column_display_mapping = column_mapping

        # Apply configuration if we're already shown
        if self._is_shown:
            self._apply_configuration()
        return self

    def show(self):
        """Show the table and apply all configuration."""
        self._is_shown = True
        if self._dataframe is not None:
            self._apply_configuration()
        super().show()
        return self

    # Dynamic/Runtime Methods
    def set_visible_columns(self, columns: List[str]):
        """Set which columns are visible (runtime change)."""
        self._config.default_visible_columns = columns
        if self._is_shown:
            self._apply_column_visibility()
        return self

    def hide_columns(self, columns: List[str]):
        """Hide specific columns (runtime change)."""
        current_visible = self._config.default_visible_columns or []
        new_visible = [col for col in current_visible if col not in columns]
        return self.set_visible_columns(new_visible)

    def show_columns(self, columns: List[str]):
        """Show specific columns (runtime change)."""
        current_visible = self._config.default_visible_columns or []
        new_visible = list(set(current_visible + columns))
        return self.set_visible_columns(new_visible)

    def resize_column(self, column: str, width: int):
        """Resize specific column (runtime change)."""
        if not self._config.column_widths:
            self._config.column_widths = {}
        self._config.column_widths[column] = width
        if self._is_shown:
            self._apply_column_width(column, width)
        return self

    def auto_resize_columns(self):
        """Re-trigger auto-sizing for all columns."""
        if self._is_shown:
            self._apply_auto_sizing()
        return self

    def hide_toolbar(self):
        """Hide the toolbar (runtime change)."""
        self._config.show_toolbar = False
        if self._is_shown:
            self.toolbar.hide()
        return self

    def show_toolbar(self):
        """Show the toolbar (runtime change)."""
        self._config.show_toolbar = True
        if self._is_shown:
            self.toolbar.show()
        return self
```

#### 3. Clean Column Width Logic
```python
def _apply_column_configuration(self):
    """Apply column configuration with clear precedence."""
    if self._config.auto_size_columns:
        # Auto-size first
        self.table_view.auto_resize_columns(self._config.max_column_width)
    
    # Apply explicit widths (overrides auto-sizing)
    if self._config.column_widths:
        self.table_view.set_explicit_widths(self._config.column_widths)
    
    # Set editable columns
    if self._config.editable_columns:
        self.table_view.set_editable_columns(self._config.editable_columns)
    
    # Apply column visibility
    if self._config.default_visible_columns:
        self.table_view.set_visible_columns(self._config.default_visible_columns)
```

## Migration Strategy

### Phase 1: Create New System
1. Implement TableViewConfig and new CustomTableView
2. Keep old system running in parallel
3. Add comprehensive tests and documentation

### Phase 2: Migrate Modules
1. Update categorize module to use new system
2. Verify all functionality works
3. Update other modules one by one

### Phase 3: Remove Old System
1. Delete old configuration layers
2. Clean up StandardColumns to be simpler
3. Remove override chains

## Benefits of This Approach

1. **Predictable**: Configuration works as expected, no hidden overrides
2. **Discoverable**: All options in one place with good documentation
3. **Flexible**: Can handle simple and complex use cases
4. **Maintainable**: Clear separation of concerns
5. **Testable**: Easy to test different configurations
6. **Extensible**: Easy to add new configuration options

## Critical Design Considerations

### 1. Column Name Mapping (Most Complex Issue)
**Problem**: We have `db_names` (could be called `app_names`) vs `display_names`
- **db_names**: Canonical internal names ('date', 'details', 'amount')
- **display_names**: User-friendly names ('Date', 'Details', 'Amount')
- **custom_names**: Module-specific display names ('Transaction Date', 'Description')

**Requirements**:
- Under the hood, always use canonical db_names
- Allow modules to specify custom display names
- Make this as simple as possible for modules to negotiate
- Need app-wide method to handle this mapping

**Proposed Solution**:
```python
# App-wide utility for column name mapping
def map_columns(df, display_mapping=None):
    """
    Args:
        df: DataFrame with db_names as columns
        display_mapping: Optional dict {db_name: custom_display_name}
    Returns:
        (df_with_display_headers, reverse_mapping)
    """
    pass

# Module usage
df_display, column_map = map_columns(df, {
    'date': 'Transaction Date',
    'details': 'Description'
})
table_view.set_dataframe(df_display, column_mapping=column_map)
```

### 2. Editability and Database Protection
**Key Points**:
- Module determines editability based on business rules
- Database must enforce protection (non-editable columns cannot be overwritten)
- Table view just follows module's instructions
- User should never get impression they can edit protected columns

### 3. Initialization Order Flexibility
**Goal**: Order shouldn't matter (as much as possible)
```python
# All of these should work:
table.configure().set_dataframe().show()
table.set_dataframe().configure().show()
table.show().configure().set_dataframe()  # Maybe not this one
```

### 4. Optional Toolbar
**Use Cases**:
- Full interactive table (filtering, export, column management)
- Data display only (no toolbar, just show data)
- Custom toolbar (module provides own controls)

### 5. Runtime/Dynamic Changes
**Key Requirement**: Configuration is for initial setup, but users need to interact dynamically

**Dynamic Methods Needed**:
```python
# Column visibility (most common runtime changes)
table_view.set_visible_columns(['date', 'details', 'amount'])
table_view.hide_columns(['balance', 'account'])
table_view.show_columns(['tags', 'notes'])
table_view.get_visible_columns()  # Returns current visible columns

# Column sizing
table_view.resize_column('details', 60)  # Set specific column width
table_view.auto_resize_columns()  # Re-trigger auto-sizing
table_view.auto_resize_column('details')  # Auto-size specific column

# Table visibility
table_view.hide()  # Hide entire table widget
table_view.show()  # Show entire table widget

# Data updates
table_view.refresh_data()  # Reload from current dataframe
table_view.update_dataframe(new_df)  # Replace data

# Toolbar control
table_view.show_toolbar()
table_view.hide_toolbar()
table_view.toggle_toolbar()

# Filter control (if toolbar visible)
table_view.set_filter('details', 'payment')
table_view.clear_filters()
table_view.get_current_filters()
```

**Design Principle**: Dynamic methods should update both the UI state AND the internal configuration so that the changes persist if the table is refreshed/rebuilt.

## Questions for Discussion

1. Should we keep StandardColumns for default widths, or move everything to TableViewConfig?
2. How should we handle the db_names vs display_names mapping? App-wide utility?
3. Should toolbar configuration be part of the main config or separate?
4. How do we handle backward compatibility during migration?
5. Should we support configuration profiles (e.g., "compact", "detailed", "minimal")?
6. How flexible should initialization order be? What are the limits?
7. Should custom display names be stored/remembered per module?
8. Should dynamic methods return `self` for chaining? (e.g., `table.hide_columns(['a']).show_columns(['b'])`)
9. How do we handle conflicts between dynamic changes and saved user preferences?
10. Should we emit signals when columns are hidden/shown for other components to react?

## Next Steps

1. **Decide on approach** - Confirm Option 1 is the right choice
2. **Design TableViewConfig** - Finalize the configuration options
3. **Implement prototype** - Build new system alongside old one
4. **Test with categorize module** - Verify it solves the current problems
5. **Document and migrate** - Roll out to other modules
