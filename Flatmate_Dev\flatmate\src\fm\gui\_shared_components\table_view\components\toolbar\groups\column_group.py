"""
Column Group

Handles column visibility and management functionality.
Contains column visibility button and related column management features.
"""

from PySide6.QtCore import Signal
from PySide6.QtWidgets import QWidget, QHBoxLayout, QPushButton


class ColumnVisibilityButton(QPushButton):
    """Button for showing column visibility menu."""

    visibility_requested = Signal()  # Emitted when button is clicked

    def __init__(self, text="Columns", parent=None):
        """Initialize the column visibility button."""
        super().__init__(text, parent)
        self.clicked.connect(self.visibility_requested)


class ColumnGroup(QWidget):
    """Group focused solely on column management functionality."""
    
    # Signals for external communication
    column_visibility_requested = Signal()
    
    def __init__(self, parent=None):
        """Initialize the column group."""
        super().__init__(parent)

        # Set CSS class for styling
        self.setObjectName("ColumnGroup")

        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)  # Reduced from 5px to 4px for tighter spacing
        
        # Column visibility button
        self.column_visibility_button = ColumnVisibilityButton()
        layout.addWidget(self.column_visibility_button)
    
    def _connect_signals(self):
        """Connect internal signals."""
        # Forward signals from column visibility button
        self.column_visibility_button.visibility_requested.connect(
            self.column_visibility_requested)
